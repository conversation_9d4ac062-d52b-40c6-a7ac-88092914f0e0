#!/usr/bin/env python3
"""
Output validation script for WIP automation.
Validates the generated Excel file and provides detailed analysis.
"""

import pandas as pd
import os
from datetime import datetime

def validate_wip_output():
    """Validate the WIP output files"""

    # Find the most recent output files
    output_files = [f for f in os.listdir('.') if f.startswith('JuneWIP_531_') and f.endswith('.xlsx') and 'CLEAN' not in f]
    clean_files = [f for f in os.listdir('.') if f.startswith('JuneWIP_531_CLEAN_') and f.endswith('.xlsx')]

    if not output_files:
        print("❌ Main output file not found. Please run the main script first.")
        return False

    output_file = max(output_files)  # Get most recent
    clean_file = max(clean_files) if clean_files else None
    
    print("WIP OUTPUT VALIDATION REPORT")
    print("=" * 50)
    print(f"File: {output_file}")
    print(f"Generated: {datetime.fromtimestamp(os.path.getmtime(output_file))}")
    print(f"Size: {os.path.getsize(output_file):,} bytes")
    
    try:
        # Read all sheets
        with pd.ExcelFile(output_file) as xls:
            sheets = xls.sheet_names
            print(f"\nSheets found: {len(sheets)}")
            for i, sheet in enumerate(sheets, 1):
                print(f"  {i}. {sheet}")
        
        # Validate Final WIP sheet in detail
        print("\n" + "=" * 50)
        print("FINAL WIP SHEET ANALYSIS")
        print("=" * 50)
        
        final_wip = pd.read_excel(output_file, sheet_name='Final WIP')
        
        # Separate data rows from totals
        data_rows = final_wip[final_wip['Status'] != 'TOTAL']
        totals_row = final_wip[final_wip['Status'] == 'TOTAL']
        
        if len(totals_row) == 0:
            print("⚠ Warning: No totals row found")
            totals = None
        else:
            totals = totals_row.iloc[0]
        
        print(f"Total jobs in WIP: {len(data_rows):,}")
        if totals is not None:
            print(f"Total WIP Accrual: ${totals['PoC Value']:,.2f}")
            print(f"Total Estimate Value: ${totals['Estimate Value']:,.2f}")
            print(f"Total Invoiced: ${totals['Invoiced']:,.2f}")
            print(f"Total Actual Cost: ${totals['Actual Cost']:,.2f}")
        
        # Division breakdown
        print(f"\nDivision Breakdown:")
        division_stats = data_rows.groupby('Div').agg({
            'Job #': 'count',
            'PoC Value': 'sum',
            'Estimate Value': 'sum'
        }).round(2)
        division_stats.columns = ['Job Count', 'Total PoC Value', 'Total Estimate Value']
        division_stats = division_stats.sort_values('Total PoC Value', ascending=False)
        
        for div, row in division_stats.iterrows():
            print(f"  {div}: {int(row['Job Count']):3d} jobs, ${row['Total PoC Value']:>12,.2f} PoC, ${row['Total Estimate Value']:>12,.2f} Est")
        
        # Data quality checks
        print(f"\n" + "=" * 50)
        print("DATA QUALITY CHECKS")
        print("=" * 50)
        
        issues = []
        warnings = []
        
        # Check for negative values
        negative_poc = data_rows[data_rows['PoC Value'] < 0]
        if len(negative_poc) > 0:
            issues.append(f"{len(negative_poc)} jobs with negative PoC values")
        
        negative_estimates = data_rows[data_rows['Estimate Value'] < 0]
        if len(negative_estimates) > 0:
            issues.append(f"{len(negative_estimates)} jobs with negative estimate values")
        
        # Check for zero estimates
        zero_estimates = data_rows[data_rows['Estimate Value'] == 0]
        if len(zero_estimates) > 0:
            warnings.append(f"{len(zero_estimates)} jobs with zero estimate values")
        
        # Check for high completion percentages
        high_completion = data_rows[data_rows['Estimated % of Completion'] > 1.5]
        if len(high_completion) > 0:
            warnings.append(f"{len(high_completion)} jobs with >150% completion")
        
        # Check for very high PoC values
        high_poc = data_rows[data_rows['PoC Value'] > 500000]  # $500k threshold
        if len(high_poc) > 0:
            warnings.append(f"{len(high_poc)} jobs with PoC value >$500k")
        
        # Display issues and warnings
        if issues:
            print("❌ CRITICAL ISSUES:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ No critical issues found")
        
        if warnings:
            print("\n⚠ WARNINGS:")
            for warning in warnings:
                print(f"   - {warning}")
        else:
            print("✅ No warnings")
        
        # Top jobs analysis
        print(f"\n" + "=" * 50)
        print("TOP JOBS ANALYSIS")
        print("=" * 50)
        
        # Top 10 by PoC value
        top_poc = data_rows.nlargest(10, 'PoC Value')[['Job #', 'Div', 'PoC Value', 'Estimate Value', 'Estimated % of Completion']]
        print("Top 10 jobs by PoC Value:")
        for i, (_, row) in enumerate(top_poc.iterrows(), 1):
            print(f"  {i:2d}. {row['Job #']} ({row['Div']}) - ${row['PoC Value']:>10,.2f} PoC ({row['Estimated % of Completion']:>5.1%} complete)")
        
        # Summary statistics
        print(f"\n" + "=" * 50)
        print("SUMMARY STATISTICS")
        print("=" * 50)
        
        print(f"Average estimate value: ${data_rows['Estimate Value'].mean():,.2f}")
        print(f"Median estimate value: ${data_rows['Estimate Value'].median():,.2f}")
        print(f"Average PoC value: ${data_rows['PoC Value'].mean():,.2f}")
        print(f"Median PoC value: ${data_rows['PoC Value'].median():,.2f}")
        print(f"Average completion %: {data_rows['Estimated % of Completion'].mean():.1%}")
        print(f"Median completion %: {data_rows['Estimated % of Completion'].median():.1%}")
        
        # Jobs with no activity
        no_activity = data_rows[(data_rows['PoC Value'] == 0) & (data_rows['Actual Cost'] == 0)]
        if len(no_activity) > 0:
            print(f"\nJobs with no activity: {len(no_activity)}")
        
        # Validate clean file if it exists
        if clean_file:
            print(f"\n" + "=" * 50)
            print("CLEAN WIP FILE VALIDATION")
            print("=" * 50)

            try:
                clean_wip = pd.read_excel(clean_file, sheet_name='Clean WIP')
                clean_data_rows = clean_wip[clean_wip['Status'] != 'TOTAL']
                clean_totals = clean_wip[clean_wip['Status'] == 'TOTAL']

                print(f"Clean file: {clean_file}")
                print(f"Clean jobs count: {len(clean_data_rows)}")

                if len(clean_totals) > 0:
                    print(f"Clean WIP total: ${clean_totals.iloc[0]['PoC Value']:,.2f}")

                # Validate no zero or negative values
                zero_poc = clean_data_rows[clean_data_rows['PoC Value'] <= 0]
                negative_estimates = clean_data_rows[clean_data_rows['Estimate Value'] < 0]

                if len(zero_poc) == 0 and len(negative_estimates) == 0:
                    print("✅ Clean file validation PASSED - No zero/negative values")
                else:
                    print(f"❌ Clean file validation FAILED - Found {len(zero_poc)} zero PoC, {len(negative_estimates)} negative estimates")
                    issues.append("Clean file contains zero or negative values")

                # Check sheets
                with pd.ExcelFile(clean_file) as xls:
                    clean_sheets = xls.sheet_names
                    expected_clean_sheets = ['Clean WIP', 'Division Summary', 'Top Jobs']

                    for sheet in expected_clean_sheets:
                        if sheet not in clean_sheets:
                            issues.append(f"Missing clean sheet: {sheet}")

                    print(f"Clean file sheets: {clean_sheets}")

            except Exception as e:
                print(f"❌ Error validating clean file: {e}")
                issues.append("Clean file validation error")
        else:
            print(f"\n⚠ Clean WIP file not found")

        print(f"\n" + "=" * 50)
        print("VALIDATION COMPLETE")
        print("=" * 50)

        if len(issues) == 0:
            print("✅ All output file validation PASSED")
            return True
        else:
            print("❌ Output file validation FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        return False

def compare_with_previous():
    """Compare with previous WIP if available"""
    # This would compare with previous month's WIP file if available
    # For now, just a placeholder
    print("\n" + "=" * 50)
    print("COMPARISON WITH PREVIOUS PERIOD")
    print("=" * 50)
    print("(Previous period comparison not implemented)")

if __name__ == "__main__":
    success = validate_wip_output()
    compare_with_previous()
    
    if success:
        print("\n🎉 WIP automation validation completed successfully!")
    else:
        print("\n⚠ WIP automation validation completed with issues.")
