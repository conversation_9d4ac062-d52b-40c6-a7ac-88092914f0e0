# Changelog

All notable changes to the WIP Automation project will be documented in this file.

## [1.2.0] - 2025-06-11

### Added
- Comprehensive test suite (`test_wip_automation.py`)
- Output validation tool (`validate_output.py`)
- Improved debug output for file processing
- Git repository setup with proper structure
- Documentation (README.md, CHANGELOG.md)

### Fixed
- QuickBooks file header detection and parsing
- Division extraction function for invalid job numbers
- Column name handling for Excel files with unnamed columns

### Changed
- Enhanced error handling and logging
- Improved job number extraction regex
- Better data quality validation

## [1.1.0] - 2025-06-11

### Added
- Debug output for troubleshooting file reading issues
- Better error messages for missing columns
- Manual column name setting for problematic Excel files

### Fixed
- Excel file reading when headers are not properly detected
- Column name processing for QuickBooks exports

## [1.0.0] - 2025-06-11

### Added
- Initial working version of WIP automation script
- Percentage of Completion (PoC) calculation method
- Division-based cost percentage calculations
- Support for multiple QuickBooks export formats
- Excel output with 8 comprehensive sheets
- Job number extraction and validation
- Prior month WIP integration

### Features
- Processes 6 different input file types
- Calculates WIP values using industry-standard PoC method
- Generates detailed Excel reports
- <PERSON><PERSON> partial invoicing scenarios
- Division breakdown and analysis
- Top jobs reporting

### Technical Details
- Python 3.x compatible
- Uses pandas for data processing
- openpyxl for Excel file handling
- Regex-based job number extraction
- Configurable division cost percentages
