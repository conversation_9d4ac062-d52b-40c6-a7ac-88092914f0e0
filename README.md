# WIP Automation Project

A Python automation tool for calculating Work in Progress (WIP) values from QuickBooks data.

## 🎯 Overview

This project automates the monthly WIP calculation process by:
- Reading QuickBooks export files (Excel format)
- Processing production lists, invoice data, estimates, and costs
- Calculating WIP values using Percentage of Completion (PoC) method
- Generating comprehensive Excel reports

## 📊 Key Features

- **Automated Data Processing**: Reads multiple QuickBooks Excel exports
- **Smart Job Detection**: Extracts job numbers in format XX-XXXX-XXX
- **Division-Based Calculations**: Uses division-specific cost percentages
- **PoC Method**: Implements Percentage of Completion accounting
- **Comprehensive Reporting**: Generates detailed Excel workbook with 8 sheets
- **Data Validation**: Built-in quality checks and validation

## 🚀 Quick Start

### Prerequisites
```bash
pip install pandas openpyxl numpy
```

### Usage
1. Place your QuickBooks export files in a folder (e.g., `WIP 6-1-25/`)
2. Update the folder path in the script if needed
3. Run the main script:
```bash
python fixed-wip-automation-script.py
```

### Required Input Files
- `May Production List.xlsx` - Jobs with expenses in the month
- `May Invoiced List.xlsx` - Jobs invoiced in the month  
- `Invoiced Amount Data.xlsx` - Total invoiced amounts per job
- `Actual Cost Data.xlsx` - Total actual costs per job
- `Estimate Values Data.xlsx` - Job estimates from DASH
- `Prior Month WIP.xlsx` - Previous month's WIP jobs

## 📈 Output

The script generates two Excel workbooks:

### Main Workbook: `JuneWIP_531_[timestamp].xlsx`
1. **Estimate Data** - Job estimates
2. **Invoicing Data** - Invoiced amounts
3. **Cost Data** - Actual costs
4. **May Production List** - Jobs with activity
5. **Possible WIP** - Candidate WIP jobs
6. **May Invoice** - Invoiced jobs
7. **Current WIP List** - Final WIP job list
8. **Final WIP** - Complete WIP calculations with totals

### Clean Workbook: `JuneWIP_531_CLEAN_[timestamp].xlsx`
1. **Clean WIP** - Filtered WIP jobs (removes $0 PoC and negative values)
2. **Division Summary** - Summary by division
3. **Top Jobs** - Top 20 jobs by PoC value

The clean workbook provides a focused view with only meaningful WIP items for easier analysis.

## 🧪 Testing

Run the comprehensive test suite:
```bash
python test_wip_automation.py
```

Validate output quality:
```bash
python validate_output.py
```

## 📋 Division Cost Percentages

| Division | Cost % |
|----------|--------|
| CON      | 29%    |
| ENV      | 45%    |
| MIT      | 9%     |
| MLD      | 7%     |
| REM      | 57%    |
| REP      | 48%    |
| RFG      | 57%    |
| TMP      | 33%    |
| TRM      | 16%    |
| WTR      | 24%    |

## 🔧 Configuration

Update division cost percentages in `DIVISION_COST_PERCENTAGES` dictionary.
Update file paths in the `calculate_wip()` function.

## 📊 Recent Results

**Last Run Summary:**
- Total WIP Jobs: 1,415 (233 in clean workbook)
- Total WIP Accrual: $1,199,154.85
- Execution Time: ~1.2 seconds
- Top Division: REP (824 jobs, $1,014,534.27)
- Clean WIP: Removes 1,182 jobs with $0 PoC values

## 🛠 Troubleshooting

### Common Issues
1. **File Not Found**: Ensure all input files are in the correct folder
2. **Column Errors**: QuickBooks exports may have different header rows
3. **Job Number Format**: Ensure job numbers follow XX-XXXX-XXX pattern

### Debug Mode
The script includes debug output showing file processing progress.

## 📝 File Structure

```
WIP-Automation/
├── fixed-wip-automation-script.py  # Main script
├── test_wip_automation.py          # Test suite
├── validate_output.py              # Output validation
├── README.md                       # This file
├── .gitignore                      # Git ignore rules
└── WIP 6-1-25/                    # Data folder (not in git)
    ├── May Production List.xlsx
    ├── May Invoiced List.xlsx
    ├── Invoiced Amount Data.xlsx
    ├── Actual Cost Data.xlsx
    ├── Estimate Values Data.xlsx
    └── Prior Month WIP.xlsx
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests to ensure everything works
5. Submit a pull request

## 📄 License

This project is for internal use. Please ensure compliance with your organization's data handling policies.

## 🔄 Version History

- **v1.0** - Initial working version with PoC calculations
- **v1.1** - Added comprehensive testing and validation
- **v1.2** - Improved QuickBooks file parsing and error handling

## 📞 Support

For issues or questions, please check the troubleshooting section or create an issue in the repository.

---

**⚠️ Important**: This tool processes financial data. Always verify results and maintain backups of your data files.
